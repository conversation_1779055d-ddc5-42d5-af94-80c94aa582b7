import { IAddTaskButtonProps } from "@/types";
import LoadingButton from "./LoadingButton";

interface AddTaskButtonProps extends IAddTaskButtonProps {
  loading?: boolean;
}

const AddTaskButton = ({ handleOpen, loading = false }: AddTaskButtonProps) => {
  return (
    <LoadingButton
      variant="outlined"
      sx={{
        width: "100%",
        borderStyle: "dashed",
        height: "50px",
        borderWidth: "2px",
        borderRadius: "4px",
        fontSize: "20px",
        bgcolor: "#fff",
      }}
      color="primary"
      onClick={handleOpen}
      loading={loading}
      loadingText="Adding..."
    >
      +
    </LoadingButton>
  );
};

export default AddTaskButton;
