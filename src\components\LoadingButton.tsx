import { Button, CircularProgress, ButtonProps } from "@mui/material";
import { ReactNode } from "react";

interface LoadingButtonProps extends Omit<ButtonProps, 'children'> {
  loading?: boolean;
  children: ReactNode;
  loadingText?: string;
}

const LoadingButton = ({ 
  loading = false, 
  children, 
  loadingText,
  disabled,
  startIcon,
  ...props 
}: LoadingButtonProps) => {
  return (
    <Button
      {...props}
      disabled={disabled || loading}
      startIcon={loading ? <CircularProgress size={20} color="inherit" /> : startIcon}
    >
      {loading ? (loadingText || "Loading...") : children}
    </Button>
  );
};

export default LoadingButton;
