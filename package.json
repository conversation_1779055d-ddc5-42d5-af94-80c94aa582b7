{"name": "kanban-todo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "api": "json-server --watch public/db.json --port 4000"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@mui/material": "^7.1.2", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.5.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tanstack/react-query": "^5.81.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "add": "^2.0.6", "axios": "^1.10.0", "eslint": "^9", "eslint-config-next": "15.3.4", "json-server": "^1.0.0-beta.3", "typescript": "^5", "yarn": "^1.22.22", "zustand": "^5.0.6"}}